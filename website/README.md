# BitTorrent Client Website

This website provides comprehensive documentation for the Simple BitTorrent Client (fast-peer) project.

## Website Structure

- **index.html** - Main landing page with project overview and features
- **documentation.html** - Complete technical documentation with all commands and classes
- **llm-summary.html** - Structured summary optimized for LLM consumption
- **styles.css** - Modern, responsive CSS styling
- **script.js** - Interactive JavaScript functionality

## Features

### Main Page (index.html)
- Project overview and key features
- Quick start guide
- Architecture overview
- GitHub repository link
- Responsive design with mobile navigation

### Documentation Page (documentation.html)
- Complete command reference
- Core class documentation
- Usage examples
- Troubleshooting guide
- Sidebar navigation for easy browsing

### LLM Summary Page (llm-summary.html)
- Structured technical summary
- Copy-all functionality for LLM input
- Comprehensive project details
- Technical specifications
- Performance optimizations

## Design Features

- **Responsive Design** - Works on desktop, tablet, and mobile
- **Modern UI** - Clean, professional interface with smooth animations
- **Code Highlighting** - Syntax highlighting for code examples
- **Interactive Elements** - Hover effects, smooth scrolling, copy functionality
- **Accessibility** - Proper semantic HTML and keyboard navigation

## Technologies Used

- HTML5 with semantic markup
- CSS3 with modern features (Grid, Flexbox, Custom Properties)
- Vanilla JavaScript for interactivity
- Prism.js for syntax highlighting
- Font Awesome for icons

## Usage

1. Open `index.html` in a web browser
2. Navigate between pages using the top navigation
3. Use the documentation sidebar for quick access to sections
4. Copy LLM summary content using the "Copy All for LLM" button

## GitHub Integration

The website includes direct links to the GitHub repository:
https://github.com/patelchaitany/bit_torrent_v1

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## Local Development

Simply open the HTML files in a web browser. No build process required.

For best experience, serve the files through a local web server:

```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Node.js (if you have http-server installed)
npx http-server
```

Then visit `http://localhost:8000`
