<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - BitTorrent Client (fast-peer)</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⬇️</text></svg>">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .doc-nav {
            position: fixed;
            left: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            padding: 2rem 0;
            z-index: 100;
        }
        
        .doc-content {
            margin-left: 250px;
            padding: 2rem;
            max-width: 900px;
        }
        
        .doc-nav ul {
            list-style: none;
            padding: 0 1rem;
        }
        
        .doc-nav li {
            margin-bottom: 0.5rem;
        }
        
        .doc-nav a {
            display: block;
            padding: 0.5rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .doc-nav a:hover,
        .doc-nav a.active {
            background: var(--primary-color);
            color: white;
        }
        
        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .doc-section:last-child {
            border-bottom: none;
        }
        
        .doc-section h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
        
        .doc-section h3 {
            font-size: 1.5rem;
            margin: 2rem 0 1rem;
            color: var(--text-primary);
        }
        
        .doc-section h4 {
            font-size: 1.25rem;
            margin: 1.5rem 0 0.75rem;
            color: var(--text-primary);
        }
        
        .command-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }
        
        .command-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .command-syntax {
            background: var(--bg-dark);
            color: #e5e7eb;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Fira Code', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        .parameter-table th,
        .parameter-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .parameter-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .class-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .class-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .method-list {
            list-style: none;
            padding: 0;
        }
        
        .method-list li {
            background: white;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid var(--primary-color);
        }
        
        .method-name {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .method-desc {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .doc-nav {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .doc-nav.active {
                transform: translateX(0);
            }
            
            .doc-content {
                margin-left: 0;
                padding: 1rem;
            }
            
            .doc-toggle {
                display: block;
                position: fixed;
                top: 80px;
                left: 1rem;
                z-index: 101;
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 0.5rem;
                border-radius: 0.5rem;
                cursor: pointer;
            }
        }
        
        .doc-toggle {
            display: none;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-download"></i>
                <span>BitTorrent Client</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="documentation.html" class="nav-link active">Documentation</a>
                </li>
                <li class="nav-item">
                    <a href="llm-summary.html" class="nav-link">LLM Summary</a>
                </li>
                <li class="nav-item">
                    <a href="https://github.com/patelchaitany/bit_torrent_v1" class="nav-link github-link" target="_blank">
                        <i class="fab fa-github"></i> GitHub
                    </a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <button class="doc-toggle" onclick="toggleDocNav()">
        <i class="fas fa-bars"></i>
    </button>

    <aside class="doc-nav" id="docNav">
        <ul>
            <li><a href="#installation" onclick="setActive(this)">Installation</a></li>
            <li><a href="#commands" onclick="setActive(this)">Commands</a></li>
            <li><a href="#basic-commands" onclick="setActive(this)">Basic Commands</a></li>
            <li><a href="#magnet-commands" onclick="setActive(this)">Magnet Commands</a></li>
            <li><a href="#dht-commands" onclick="setActive(this)">DHT Commands</a></li>
            <li><a href="#core-classes" onclick="setActive(this)">Core Classes</a></li>
            <li><a href="#peer-manager" onclick="setActive(this)">PeerManager</a></li>
            <li><a href="#piece-manager" onclick="setActive(this)">PieceManager</a></li>
            <li><a href="#dht-class" onclick="setActive(this)">DHT Class</a></li>
            <li><a href="#metadata-manager" onclick="setActive(this)">MetadataManager</a></li>
            <li><a href="#examples" onclick="setActive(this)">Usage Examples</a></li>
            <li><a href="#troubleshooting" onclick="setActive(this)">Troubleshooting</a></li>
        </ul>
    </aside>

    <main class="doc-content">
        <h1>BitTorrent Client Documentation</h1>
        <p>Complete documentation for the Simple BitTorrent Client (fast-peer) implementation.</p>

        <section id="installation" class="doc-section">
            <h2><i class="fas fa-download"></i> Installation & Requirements</h2>
            <p>This BitTorrent client requires Python 3.10+ and has minimal dependencies for maximum compatibility.</p>
            
            <h3>System Requirements</h3>
            <ul>
                <li><strong>Python 3.10+</strong> (tested on Linux)</li>
                <li><strong>requests</strong> library for HTTP tracker support</li>
                <li><strong>libtorrent</strong> (optional, only for seeder.py)</li>
            </ul>

            <h3>Installation Steps</h3>
            <div class="command-syntax">pip install requests
# Optional for seeding functionality
pip install libtorrent</div>

            <h3>Project Structure</h3>
            <div class="command-syntax">bit_torrent_v1/
├── app/
│   ├── main.py          # Main client implementation
│   ├── seeder.py        # Seeding functionality
│   ├── test.py          # Testing utilities
│   └── *.torrent        # Sample torrent files
├── README.md
└── your_program.sh      # Execution script</div>
        </section>

        <section id="commands" class="doc-section">
            <h2><i class="fas fa-terminal"></i> Command Reference</h2>
            <p>The BitTorrent client supports multiple commands for different operations. All commands are executed through the main.py file.</p>
        </section>

        <section id="basic-commands" class="doc-section">
            <h3>Basic Commands</h3>

            <div class="command-card">
                <div class="command-title">decode</div>
                <p>Decode bencoded data and display as JSON.</p>
                <div class="command-syntax">python app/main.py decode &lt;bencoded_string&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py decode "d8:announce9:test.com4:name4:teste"</div>
            </div>

            <div class="command-card">
                <div class="command-title">info</div>
                <p>Display detailed information about a torrent file.</p>
                <div class="command-syntax">python app/main.py info &lt;torrent_file&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py info sample.torrent</div>
                <p><strong>Output includes:</strong> Tracker URL, file length, info hash, piece length, and piece hashes.</p>
            </div>

            <div class="command-card">
                <div class="command-title">peers</div>
                <p>Discover and list available peers for a torrent.</p>
                <div class="command-syntax">python app/main.py peers &lt;torrent_file&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py peers sample.torrent</div>
            </div>

            <div class="command-card">
                <div class="command-title">handshake</div>
                <p>Perform BitTorrent handshake with a specific peer.</p>
                <div class="command-syntax">python app/main.py handshake &lt;torrent_file&gt; &lt;peer_ip:port&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py handshake sample.torrent *************:6881</div>
            </div>

            <div class="command-card">
                <div class="command-title">download_piece</div>
                <p>Download a specific piece from a torrent.</p>
                <div class="command-syntax">python app/main.py download_piece -o &lt;output_file&gt; &lt;torrent_file&gt; &lt;piece_index&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py download_piece -o piece0.dat sample.torrent 0</div>
            </div>

            <div class="command-card">
                <div class="command-title">download</div>
                <p>Download the complete file from a torrent.</p>
                <div class="command-syntax">python app/main.py download -o &lt;output_file&gt; &lt;torrent_file&gt;</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py download -o myfile.zip sample.torrent</div>
            </div>
        </section>

        <section id="magnet-commands" class="doc-section">
            <h3>Magnet Link Commands</h3>

            <div class="command-card">
                <div class="command-title">magnet_parse</div>
                <p>Parse and display information from a magnet link.</p>
                <div class="command-syntax">python app/main.py magnet_parse "&lt;magnet_link&gt;"</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py magnet_parse "magnet:?xt=urn:btih:1234567890abcdef..."</div>
            </div>

            <div class="command-card">
                <div class="command-title">magnet_handshake</div>
                <p>Perform handshake using magnet link information.</p>
                <div class="command-syntax">python app/main.py magnet_handshake "&lt;magnet_link&gt;"</div>
            </div>

            <div class="command-card">
                <div class="command-title">magnet_info</div>
                <p>Retrieve and display metadata from magnet link.</p>
                <div class="command-syntax">python app/main.py magnet_info "&lt;magnet_link&gt;"</div>
            </div>

            <div class="command-card">
                <div class="command-title">magnet_download_piece</div>
                <p>Download a specific piece using magnet link.</p>
                <div class="command-syntax">python app/main.py magnet_download_piece -o &lt;output_file&gt; "&lt;magnet_link&gt;" &lt;piece_index&gt;</div>
            </div>

            <div class="command-card">
                <div class="command-title">magnet_download</div>
                <p>Download complete file using magnet link.</p>
                <div class="command-syntax">python app/main.py magnet_download -o &lt;output_file&gt; "&lt;magnet_link&gt;"</div>
                <p><strong>Example:</strong></p>
                <div class="command-syntax">python app/main.py magnet_download -o file.zip "magnet:?xt=urn:btih:..."</div>
            </div>
        </section>

        <section id="dht-commands" class="doc-section">
            <h3>DHT Commands</h3>

            <div class="command-card">
                <div class="command-title">dht</div>
                <p>Use DHT for peer discovery and download files.</p>
                <div class="command-syntax">python app/main.py dht -o &lt;output_file&gt; "&lt;magnet_link&gt;"</div>
                <p><strong>Features:</strong></p>
                <ul>
                    <li>Trackerless peer discovery</li>
                    <li>DHT node bootstrapping</li>
                    <li>Automatic peer replenishment</li>
                    <li>Metadata exchange via DHT</li>
                </ul>
            </div>

            <div class="command-card">
                <div class="command-title">nhandshake</div>
                <p>Perform negotiated handshake with extended protocol support.</p>
                <div class="command-syntax">python app/main.py nhandshake &lt;torrent_file&gt; &lt;peer_ip:port&gt;</div>
            </div>
        </section>

        <section id="core-classes" class="doc-section">
            <h2><i class="fas fa-code"></i> Core Classes</h2>
            <p>The BitTorrent client is built around several key classes that handle different aspects of the protocol.</p>
        </section>

        <section id="peer-manager" class="doc-section">
            <div class="class-card">
                <div class="class-name">PeerManager</div>
                <p>Manages peer connections, DHT integration, and peer discovery strategies.</p>

                <h4>Key Responsibilities:</h4>
                <ul>
                    <li>Maintain active peer connections</li>
                    <li>Integrate with DHT for peer discovery</li>
                    <li>Handle peer connection lifecycle</li>
                    <li>Manage unchoked peer list</li>
                </ul>

                <h4>Key Methods:</h4>
                <ul class="method-list">
                    <li>
                        <div class="method-name">add_peer(peer_info)</div>
                        <div class="method-desc">Add a new peer to the connection pool</div>
                    </li>
                    <li>
                        <div class="method-name">get_best_peers(count)</div>
                        <div class="method-desc">Get the best performing peers based on throughput</div>
                    </li>
                    <li>
                        <div class="method-name">replenish_peers()</div>
                        <div class="method-desc">Use DHT to find new peers when connections drop</div>
                    </li>
                    <li>
                        <div class="method-name">cleanup_dead_peers()</div>
                        <div class="method-desc">Remove disconnected or unresponsive peers</div>
                    </li>
                </ul>
            </div>
        </section>

        <section id="piece-manager" class="doc-section">
            <div class="class-card">
                <div class="class-name">PieceManager</div>
                <p>Handles piece downloading, verification, and distribution among peers.</p>

                <h4>Key Responsibilities:</h4>
                <ul>
                    <li>Track which pieces need to be downloaded</li>
                    <li>Assign pieces to available peers</li>
                    <li>Handle piece verification and retry logic</li>
                    <li>Manage download priorities</li>
                </ul>

                <h4>Key Methods:</h4>
                <ul class="method-list">
                    <li>
                        <div class="method-name">get_piece_for_peer(have_pieces)</div>
                        <div class="method-desc">Get next piece to download based on peer's available pieces</div>
                    </li>
                    <li>
                        <div class="method-name">mark_failed(idx)</div>
                        <div class="method-desc">Mark a piece as failed and add back to needed pieces</div>
                    </li>
                    <li>
                        <div class="method-name">get_size()</div>
                        <div class="method-desc">Get number of remaining pieces to download</div>
                    </li>
                </ul>
            </div>
        </section>

        <section id="dht-class" class="doc-section">
            <div class="class-card">
                <div class="class-name">DHT (Distributed Hash Table)</div>
                <p>Implements BEP 5 DHT protocol for trackerless peer discovery.</p>

                <h4>Key Responsibilities:</h4>
                <ul>
                    <li>Bootstrap with known DHT nodes</li>
                    <li>Handle DHT queries (ping, find_node, get_peers)</li>
                    <li>Maintain routing table of known nodes</li>
                    <li>Discover peers for specific info hashes</li>
                </ul>

                <h4>DHT Messages Supported:</h4>
                <ul class="method-list">
                    <li>
                        <div class="method-name">ping</div>
                        <div class="method-desc">Basic connectivity test between DHT nodes</div>
                    </li>
                    <li>
                        <div class="method-name">find_node</div>
                        <div class="method-desc">Find nodes closest to a target node ID</div>
                    </li>
                    <li>
                        <div class="method-name">get_peers</div>
                        <div class="method-desc">Find peers downloading/seeding a specific torrent</div>
                    </li>
                    <li>
                        <div class="method-name">announce_peer</div>
                        <div class="method-desc">Announce that this node is downloading/seeding</div>
                    </li>
                </ul>

                <h4>Bootstrap Nodes:</h4>
                <ul>
                    <li>router.bittorrent.com:6881</li>
                    <li>dht.transmissionbt.com:6881</li>
                    <li>router.utorrent.com:6881</li>
                    <li>dht.aelitis.com:6881</li>
                    <li>dht.libtorrent.org:25401</li>
                </ul>
            </div>
        </section>

        <section id="metadata-manager" class="doc-section">
            <div class="class-card">
                <div class="class-name">MetadataManager</div>
                <p>Manages torrent metadata exchange for magnet links.</p>

                <h4>Key Responsibilities:</h4>
                <ul>
                    <li>Download metadata pieces from peers</li>
                    <li>Assemble complete metadata from pieces</li>
                    <li>Handle metadata piece verification</li>
                    <li>Retry failed metadata downloads</li>
                </ul>

                <h4>Key Methods:</h4>
                <ul class="method-list">
                    <li>
                        <div class="method-name">get_next_missing()</div>
                        <div class="method-desc">Get next metadata piece that needs to be downloaded</div>
                    </li>
                    <li>
                        <div class="method-name">add_piece(index, data)</div>
                        <div class="method-desc">Add a downloaded metadata piece</div>
                    </li>
                    <li>
                        <div class="method-name">is_complete()</div>
                        <div class="method-desc">Check if all metadata pieces have been downloaded</div>
                    </li>
                    <li>
                        <div class="method-name">assemble()</div>
                        <div class="method-desc">Assemble complete metadata from all pieces</div>
                    </li>
                </ul>
            </div>
        </section>

        <section id="examples" class="doc-section">
            <h2><i class="fas fa-play"></i> Usage Examples</h2>

            <h3>Basic File Download</h3>
            <div class="command-syntax"># Download a complete file
python app/main.py download -o ubuntu.iso ubuntu.torrent

# Download specific piece only
python app/main.py download_piece -o piece0.dat ubuntu.torrent 0</div>

            <h3>Magnet Link Usage</h3>
            <div class="command-syntax"># Parse magnet link
python app/main.py magnet_parse "magnet:?xt=urn:btih:1234567890abcdef..."

# Download from magnet link
python app/main.py magnet_download -o file.zip "magnet:?xt=urn:btih:..."</div>

            <h3>DHT-based Download</h3>
            <div class="command-syntax"># Use DHT for trackerless download
python app/main.py dht -o file.zip "magnet:?xt=urn:btih:..."</div>

            <h3>Peer Discovery</h3>
            <div class="command-syntax"># Discover peers for a torrent
python app/main.py peers sample.torrent

# Perform handshake with specific peer
python app/main.py handshake sample.torrent *************:6881</div>

            <h3>Seeding (requires libtorrent)</h3>
            <div class="command-syntax"># Run seeder for localhost testing
python app/seeder.py</div>
        </section>

        <section id="troubleshooting" class="doc-section">
            <h2><i class="fas fa-wrench"></i> Troubleshooting</h2>

            <h3>Common Issues</h3>

            <div class="command-card">
                <div class="command-title">No peers found</div>
                <p><strong>Symptoms:</strong> Download fails with "no peers available"</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Check if torrent is still active/seeded</li>
                    <li>Try using DHT command for trackerless discovery</li>
                    <li>Verify network connectivity and firewall settings</li>
                    <li>Check if tracker URLs are accessible</li>
                </ul>
            </div>

            <div class="command-card">
                <div class="command-title">Slow download speeds</div>
                <p><strong>Symptoms:</strong> Downloads are slower than expected</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Increase number of concurrent connections</li>
                    <li>Check network bandwidth and latency</li>
                    <li>Try different torrents to isolate the issue</li>
                    <li>Monitor peer connection quality</li>
                </ul>
            </div>

            <div class="command-card">
                <div class="command-title">DHT not working</div>
                <p><strong>Symptoms:</strong> DHT commands fail or timeout</p>
                <p><strong>Solutions:</strong></p>
                <ul>
                    <li>Check UDP port 6881 is not blocked</li>
                    <li>Verify internet connectivity</li>
                    <li>Try different bootstrap nodes</li>
                    <li>Check firewall/NAT configuration</li>
                </ul>
            </div>

            <h3>Debug Mode</h3>
            <p>Enable verbose logging by modifying the log level in main.py:</p>
            <div class="command-syntax"># Enable debug logging
def log(message, level="INFO"):
    if level in ["DEBUG", "INFO", "ERROR"]:
        print(f"[{level}] {message}")</div>

            <h3>Performance Tuning</h3>
            <ul>
                <li><strong>Concurrent connections:</strong> Adjust peer connection limits</li>
                <li><strong>Piece size:</strong> Larger pieces = fewer requests but less granular progress</li>
                <li><strong>Block size:</strong> Smaller blocks = more parallel downloads</li>
                <li><strong>DHT refresh:</strong> More frequent DHT queries = better peer discovery</li>
            </ul>
        </section>
    </main>

    <script>
        function toggleDocNav() {
            const nav = document.getElementById('docNav');
            nav.classList.toggle('active');
        }
        
        function setActive(element) {
            document.querySelectorAll('.doc-nav a').forEach(a => a.classList.remove('active'));
            element.classList.add('active');
            
            // Close mobile nav
            if (window.innerWidth <= 768) {
                document.getElementById('docNav').classList.remove('active');
            }
        }
        
        // Auto-highlight current section
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.doc-section');
            const navLinks = document.querySelectorAll('.doc-nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
