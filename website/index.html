<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitTorrent Client (fast-peer) - Simple & Fast P2P File Sharing</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⬇️</text></svg>">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-download"></i>
                <span>BitTorrent Client</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">Home</a>
                </li>
                <li class="nav-item">
                    <a href="documentation.html" class="nav-link">Documentation</a>
                </li>
                <li class="nav-item">
                    <a href="llm-summary.html" class="nav-link">LLM Summary</a>
                </li>
                <li class="nav-item">
                    <a href="https://github.com/patelchaitany/bit_torrent_v1" class="nav-link github-link" target="_blank">
                        <i class="fab fa-github"></i> GitHub
                    </a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <header class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">Simple BitTorrent Client</h1>
                <p class="hero-subtitle">A compact BitTorrent client focused on fast peer acquisition and reliable downloads</p>
                <div class="hero-badges">
                    <span class="badge">Python 3.10+</span>
                    <span class="badge">DHT Support</span>
                    <span class="badge">Magnet Links</span>
                    <span class="badge">Fast Peer Discovery</span>
                </div>
                <div class="hero-buttons">
                    <a href="https://github.com/patelchaitany/bit_torrent_v1" class="btn btn-primary" target="_blank">
                        <i class="fab fa-github"></i> View on GitHub
                    </a>
                    <a href="documentation.html" class="btn btn-secondary">
                        <i class="fas fa-book"></i> Documentation
                    </a>
                </div>
            </div>
            <div class="hero-image">
                <div class="code-preview">
                    <div class="code-header">
                        <div class="code-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="code-title">main.py</span>
                    </div>
                    <pre><code class="language-python"># Download a torrent file
python main.py download -o output.file sample.torrent

# Download from magnet link
python main.py magnet_download -o file.zip "magnet:?xt=..."

# Get peer information
python main.py peers sample.torrent</code></pre>
                </div>
            </div>
        </div>
    </header>

    <main>
        <section class="features">
            <div class="container">
                <h2 class="section-title">Key Features</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3>Fast Peer Acquisition</h3>
                        <p>Immediate piece requests after handshake with no startup delay to reduce idle time and maximize download speed.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <h3>DHT Support (BEP 5)</h3>
                        <p>Trackerless peer discovery with DHT port advertisement and consumption via message type 9 for metadata/magnet support.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <h3>Auto-Replenish Peers</h3>
                        <p>Automatic peer replenishment through DHT lookups when connections drop, ensuring continuous download progress.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-magnet"></i>
                        </div>
                        <h3>Magnet Link Support</h3>
                        <p>Optional magnet metadata downloader with full support for magnet links and metadata exchange protocols.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <h3>PEX Support</h3>
                        <p>Peer Exchange (PEX) support where available using ut_pex for efficient peer discovery and sharing.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3>Parallel Downloads</h3>
                        <p>Parallel block requests per piece with configurable concurrency for higher throughput and faster downloads.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="overview">
            <div class="container">
                <div class="overview-content">
                    <div class="overview-text">
                        <h2>Project Overview</h2>
                        <p>This project implements a simple BitTorrent client designed for experimentation, research, and lightweight seeding. It's not intended as a production-grade client but rather as a tool for:</p>
                        <ul>
                            <li><strong>Testing swarm behavior</strong> - Analyze how peers interact in BitTorrent swarms</li>
                            <li><strong>Research on peer discovery</strong> - Study different strategies for finding and connecting to peers</li>
                            <li><strong>Small-scale seeding</strong> - Lightweight seeding implementation for sharing files</li>
                            <li><strong>Educational purposes</strong> - Learn how BitTorrent protocol works under the hood</li>
                        </ul>
                        <p>The client emphasizes speed and efficiency with immediate piece requests, DHT support, and intelligent peer management.</p>
                    </div>
                    <div class="overview-stats">
                        <div class="stat-card">
                            <div class="stat-number">2000+</div>
                            <div class="stat-label">Lines of Code</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">Commands</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">5</div>
                            <div class="stat-label">Core Classes</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="quick-start">
            <div class="container">
                <h2 class="section-title">Quick Start</h2>
                <div class="quick-start-content">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Install Requirements</h3>
                            <pre><code class="language-bash">pip install requests
# Optional: pip install libtorrent (for seeder.py)</code></pre>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Download a File</h3>
                            <pre><code class="language-bash">python app/main.py download -o myfile.zip sample.torrent</code></pre>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Use Magnet Links</h3>
                            <pre><code class="language-bash">python app/main.py magnet_download -o file.zip "magnet:?xt=urn:btih:..."</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="architecture">
            <div class="container">
                <h2 class="section-title">Architecture</h2>
                <div class="architecture-grid">
                    <div class="arch-component">
                        <h3><i class="fas fa-cogs"></i> Core Components</h3>
                        <ul>
                            <li><strong>PeerManager</strong> - Manages peer connections and DHT integration</li>
                            <li><strong>PieceManager</strong> - Handles piece downloading and verification</li>
                            <li><strong>DHT</strong> - Distributed Hash Table for peer discovery</li>
                            <li><strong>MetadataManager</strong> - Manages torrent metadata exchange</li>
                        </ul>
                    </div>
                    <div class="arch-component">
                        <h3><i class="fas fa-exchange-alt"></i> Protocol Support</h3>
                        <ul>
                            <li><strong>BitTorrent Protocol</strong> - Standard peer-to-peer communication</li>
                            <li><strong>DHT (BEP 5)</strong> - Trackerless peer discovery</li>
                            <li><strong>PEX (ut_pex)</strong> - Peer exchange extension</li>
                            <li><strong>Magnet Links</strong> - Metadata exchange protocol</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>BitTorrent Client</h3>
                    <p>A simple, fast, and educational BitTorrent client implementation in Python.</p>
                </div>
                <div class="footer-section">
                    <h3>Links</h3>
                    <ul>
                        <li><a href="https://github.com/patelchaitany/bit_torrent_v1" target="_blank">GitHub Repository</a></li>
                        <li><a href="documentation.html">Documentation</a></li>
                        <li><a href="llm-summary.html">LLM Summary</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Technology</h3>
                    <ul>
                        <li>Python 3.10+</li>
                        <li>Asyncio</li>
                        <li>Socket Programming</li>
                        <li>BitTorrent Protocol</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 BitTorrent Client Project. Open source under MIT License.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
