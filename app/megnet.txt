Retrieval-Augmented-Generation-(RAG)-is-a-framework-that-combines-retrieval-and-generation-to-improve-large-language-model-(LLM)-performance.-Instead-of-relying-solely-on-the-model’s-parametric-memory,-RAG-uses-a-retriever-to-fetch-relevant-documents-from-an-external-knowledge-base-(like-a-vector-database-or-search-engine)-and-feeds-them-into-the-LLM-along-with-the-user’s-query.-This-allows-the-model-to-generate-answers-that-are-more-accurate,-grounded,-and-up-to-date,-while-reducing-hallucinations-and-improving-domain-specific-knowledge-access.-By-dynamically-integrating-external-information,-RAG-enables-smaller-models-to-perform-better,-keeps-knowledge-current,-and-supports-use-cases-in-customer-support,-healthcare,-legal-research,-and-enterprise-search.